import { useEffect, useRef } from 'react';
import { Chat, Message } from '@/types/chat';
import ChatHeader from './ChatHeader';
import MessageBubble from './MessageBubble';
import MessageInput from './MessageInput';

interface ChatWindowProps {
  chat: Chat;
  onSendMessage: (message: string) => void;
  onBack?: () => void;
}

export default function ChatWindow({ chat, onSendMessage, onBack }: ChatWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chat.messages]);

  return (
    <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-800">
      {/* Chat Header */}
      <ChatHeader chat={chat} onBack={onBack} />

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {chat.messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-2xl">
                {chat.avatar || '👤'}
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Start a conversation with {chat.name}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Send a message to get the conversation started!
              </p>
            </div>
          </div>
        ) : (
          <>
            {chat.messages.map((message) => (
              <MessageBubble key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <MessageInput onSendMessage={onSendMessage} />
    </div>
  );
}
