import { Chat, Message } from '@/types/chat';

const currentUserId = 'user-1';

const mockMessages: Message[] = [
  {
    id: 'msg-1',
    text: 'Hey! How are you doing?',
    timestamp: new Date('2024-01-15T10:30:00'),
    senderId: 'user-2',
    isOwn: false,
    status: 'read'
  },
  {
    id: 'msg-2',
    text: 'I\'m doing great! Just finished a big project at work. How about you?',
    timestamp: new Date('2024-01-15T10:32:00'),
    senderId: currentUserId,
    isOwn: true,
    status: 'read'
  },
  {
    id: 'msg-3',
    text: 'That\'s awesome! I\'m planning a trip to Japan next month.',
    timestamp: new Date('2024-01-15T10:35:00'),
    senderId: 'user-2',
    isOwn: false,
    status: 'read'
  },
  {
    id: 'msg-4',
    text: 'Wow, that sounds amazing! I\'ve always wanted to visit Japan. Are you going to Tokyo?',
    timestamp: new Date('2024-01-15T10:37:00'),
    senderId: currentUserId,
    isOwn: true,
    status: 'delivered'
  },
  {
    id: 'msg-5',
    text: 'Yes! Tokyo, Kyoto, and Osaka. I\'m so excited!',
    timestamp: new Date('2024-01-15T10:40:00'),
    senderId: 'user-2',
    isOwn: false,
    status: 'read'
  }
];

const workMessages: Message[] = [
  {
    id: 'work-msg-1',
    text: 'Good morning team! Ready for today\'s standup?',
    timestamp: new Date('2024-01-15T09:00:00'),
    senderId: 'user-3',
    isOwn: false,
    status: 'read'
  },
  {
    id: 'work-msg-2',
    text: 'Morning! Yes, I have my updates ready.',
    timestamp: new Date('2024-01-15T09:02:00'),
    senderId: currentUserId,
    isOwn: true,
    status: 'read'
  },
  {
    id: 'work-msg-3',
    text: 'Perfect! Let\'s start in 5 minutes.',
    timestamp: new Date('2024-01-15T09:05:00'),
    senderId: 'user-3',
    isOwn: false,
    status: 'read'
  }
];

const familyMessages: Message[] = [
  {
    id: 'family-msg-1',
    text: 'Don\'t forget about dinner this Sunday!',
    timestamp: new Date('2024-01-14T15:30:00'),
    senderId: 'user-4',
    isOwn: false,
    status: 'read'
  },
  {
    id: 'family-msg-2',
    text: 'Of course! I\'ll bring dessert. What time should I be there?',
    timestamp: new Date('2024-01-14T15:45:00'),
    senderId: currentUserId,
    isOwn: true,
    status: 'read'
  },
  {
    id: 'family-msg-3',
    text: 'Around 6 PM would be perfect. Thanks!',
    timestamp: new Date('2024-01-14T16:00:00'),
    senderId: 'user-4',
    isOwn: false,
    status: 'read'
  }
];

export const mockChats: Chat[] = [
  {
    id: 'chat-1',
    name: 'Sarah Johnson',
    avatar: '👩‍💼',
    lastMessage: 'Yes! Tokyo, Kyoto, and Osaka. I\'m so excited!',
    lastMessageTime: new Date('2024-01-15T10:40:00'),
    unreadCount: 0,
    isOnline: true,
    messages: mockMessages
  },
  {
    id: 'chat-2',
    name: 'Work Team',
    avatar: '👥',
    lastMessage: 'Perfect! Let\'s start in 5 minutes.',
    lastMessageTime: new Date('2024-01-15T09:05:00'),
    unreadCount: 2,
    isOnline: false,
    messages: workMessages
  },
  {
    id: 'chat-3',
    name: 'Mom',
    avatar: '👩‍🦳',
    lastMessage: 'Around 6 PM would be perfect. Thanks!',
    lastMessageTime: new Date('2024-01-14T16:00:00'),
    unreadCount: 0,
    isOnline: false,
    messages: familyMessages
  },
  {
    id: 'chat-4',
    name: 'Alex Chen',
    avatar: '👨‍💻',
    lastMessage: 'Let\'s catch up soon!',
    lastMessageTime: new Date('2024-01-13T14:20:00'),
    unreadCount: 1,
    isOnline: true,
    messages: []
  },
  {
    id: 'chat-5',
    name: 'Book Club',
    avatar: '📚',
    lastMessage: 'Next meeting is on Friday',
    lastMessageTime: new Date('2024-01-12T19:30:00'),
    unreadCount: 0,
    isOnline: false,
    messages: []
  }
];

export const currentUser = {
  id: currentUserId,
  name: 'You',
  avatar: '😊',
  isOnline: true
};
