export interface Message {
  id: string;
  text: string;
  timestamp: Date;
  senderId: string;
  isOwn: boolean;
  status?: 'sent' | 'delivered' | 'read';
}

export interface Chat {
  id: string;
  name: string;
  avatar?: string;
  lastMessage?: string;
  lastMessageTime?: Date;
  unreadCount?: number;
  isOnline?: boolean;
  messages: Message[];
}

export interface User {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
}
