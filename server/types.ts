export interface User {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
}

export interface Message {
  id: string;
  text: string;
  timestamp: Date;
  senderId: string;
  chatId: string;
  isOwn?: boolean;
  status?: 'sent' | 'delivered' | 'read';
}

export interface Chat {
  id: string;
  name: string;
  avatar?: string;
  participants: string[];
  lastMessage?: string;
  lastMessageTime?: Date;
  messages: Message[];
}

export interface WebSocketMessage {
  type: 'join_chat' | 'leave_chat' | 'send_message' | 'message_received' | 'user_online' | 'user_offline' | 'typing' | 'stop_typing';
  payload: any;
  userId?: string;
  chatId?: string;
  timestamp?: Date;
}

export interface ClientConnection {
  userId: string;
  userName: string;
  socket: any; // WebSocket instance
  joinedChats: Set<string>;
}
