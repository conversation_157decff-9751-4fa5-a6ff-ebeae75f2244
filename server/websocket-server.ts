import { WebSocketServer, WebSocket } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { WebSocketMessage, ClientConnection, Message } from './types';

class ChatWebSocketServer {
  private wss: WebSocketServer;
  private clients: Map<string, ClientConnection> = new Map();
  private chatRooms: Map<string, Set<string>> = new Map(); // chatId -> Set of userIds

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ port });
    this.setupServer();
    console.log(`🚀 WebSocket server running on port ${port}`);
  }

  private setupServer() {
    this.wss.on('connection', (ws: WebSocket, request) => {
      console.log('📱 New client connected');
      
      // Handle client messages
      ws.on('message', (data: Buffer) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          this.handleMessage(ws, message);
        } catch (error) {
          console.error('❌ Error parsing message:', error);
          this.sendError(ws, 'Invalid message format');
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        this.handleDisconnect(ws);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
      });
    });
  }

  private handleMessage(ws: WebSocket, message: WebSocketMessage) {
    console.log('📨 Received message:', message.type);

    switch (message.type) {
      case 'join_chat':
        this.handleJoinChat(ws, message);
        break;
      case 'leave_chat':
        this.handleLeaveChat(ws, message);
        break;
      case 'send_message':
        this.handleSendMessage(ws, message);
        break;
      case 'typing':
        this.handleTyping(ws, message);
        break;
      case 'stop_typing':
        this.handleStopTyping(ws, message);
        break;
      default:
        this.sendError(ws, `Unknown message type: ${message.type}`);
    }
  }

  private handleJoinChat(ws: WebSocket, message: WebSocketMessage) {
    const { userId, userName, chatId } = message.payload;
    
    if (!userId || !userName || !chatId) {
      this.sendError(ws, 'Missing required fields: userId, userName, chatId');
      return;
    }

    // Create or update client connection
    const clientId = this.getClientId(ws);
    const client: ClientConnection = {
      userId,
      userName,
      socket: ws,
      joinedChats: this.clients.get(clientId)?.joinedChats || new Set()
    };

    client.joinedChats.add(chatId);
    this.clients.set(clientId, client);

    // Add user to chat room
    if (!this.chatRooms.has(chatId)) {
      this.chatRooms.set(chatId, new Set());
    }
    this.chatRooms.get(chatId)!.add(userId);

    // Notify other users in the chat that this user is online
    this.broadcastToChat(chatId, {
      type: 'user_online',
      payload: { userId, userName },
      timestamp: new Date()
    }, userId);

    // Send confirmation to the user
    this.sendToClient(ws, {
      type: 'join_chat',
      payload: { success: true, chatId },
      timestamp: new Date()
    });

    console.log(`✅ User ${userName} (${userId}) joined chat ${chatId}`);
  }

  private handleLeaveChat(ws: WebSocket, message: WebSocketMessage) {
    const { userId, chatId } = message.payload;
    const clientId = this.getClientId(ws);
    const client = this.clients.get(clientId);

    if (client && chatId) {
      client.joinedChats.delete(chatId);
      
      // Remove user from chat room
      const chatRoom = this.chatRooms.get(chatId);
      if (chatRoom) {
        chatRoom.delete(userId);
        if (chatRoom.size === 0) {
          this.chatRooms.delete(chatId);
        }
      }

      // Notify other users that this user left
      this.broadcastToChat(chatId, {
        type: 'user_offline',
        payload: { userId },
        timestamp: new Date()
      }, userId);

      console.log(`👋 User ${userId} left chat ${chatId}`);
    }
  }

  private handleSendMessage(ws: WebSocket, message: WebSocketMessage) {
    const { text, chatId, senderId, senderName } = message.payload;
    
    if (!text || !chatId || !senderId) {
      this.sendError(ws, 'Missing required fields: text, chatId, senderId');
      return;
    }

    const newMessage: Message = {
      id: uuidv4(),
      text,
      timestamp: new Date(),
      senderId,
      chatId,
      status: 'sent'
    };

    // Broadcast message to all users in the chat
    this.broadcastToChat(chatId, {
      type: 'message_received',
      payload: {
        message: newMessage,
        senderName
      },
      timestamp: new Date()
    });

    console.log(`💬 Message sent in chat ${chatId} by ${senderId}: ${text}`);
  }

  private handleTyping(ws: WebSocket, message: WebSocketMessage) {
    const { userId, userName, chatId } = message.payload;
    
    this.broadcastToChat(chatId, {
      type: 'typing',
      payload: { userId, userName },
      timestamp: new Date()
    }, userId);
  }

  private handleStopTyping(ws: WebSocket, message: WebSocketMessage) {
    const { userId, chatId } = message.payload;
    
    this.broadcastToChat(chatId, {
      type: 'stop_typing',
      payload: { userId },
      timestamp: new Date()
    }, userId);
  }

  private handleDisconnect(ws: WebSocket) {
    const clientId = this.getClientId(ws);
    const client = this.clients.get(clientId);
    
    if (client) {
      // Notify all chats that this user is offline
      client.joinedChats.forEach(chatId => {
        this.broadcastToChat(chatId, {
          type: 'user_offline',
          payload: { userId: client.userId },
          timestamp: new Date()
        }, client.userId);

        // Remove user from chat room
        const chatRoom = this.chatRooms.get(chatId);
        if (chatRoom) {
          chatRoom.delete(client.userId);
          if (chatRoom.size === 0) {
            this.chatRooms.delete(chatId);
          }
        }
      });

      this.clients.delete(clientId);
      console.log(`👋 Client ${client.userName} (${client.userId}) disconnected`);
    }
  }

  private broadcastToChat(chatId: string, message: WebSocketMessage, excludeUserId?: string) {
    const chatRoom = this.chatRooms.get(chatId);
    if (!chatRoom) return;

    chatRoom.forEach(userId => {
      if (excludeUserId && userId === excludeUserId) return;
      
      // Find the client connection for this user
      for (const [clientId, client] of this.clients.entries()) {
        if (client.userId === userId && client.socket.readyState === WebSocket.OPEN) {
          this.sendToClient(client.socket, message);
          break;
        }
      }
    });
  }

  private sendToClient(ws: WebSocket, message: WebSocketMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string) {
    this.sendToClient(ws, {
      type: 'error',
      payload: { error },
      timestamp: new Date()
    });
  }

  private getClientId(ws: WebSocket): string {
    // Use a simple approach to identify clients
    // In production, you might want to use a more robust method
    return (ws as any)._clientId || ((ws as any)._clientId = uuidv4());
  }

  public getStats() {
    return {
      connectedClients: this.clients.size,
      activeChats: this.chatRooms.size,
      totalConnections: Array.from(this.clients.values()).length
    };
  }
}

// Start the server
const server = new ChatWebSocketServer(8080);

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down WebSocket server...');
  process.exit(0);
});

export default ChatWebSocketServer;
